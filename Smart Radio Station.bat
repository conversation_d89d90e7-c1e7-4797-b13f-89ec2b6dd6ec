@echo off
title Smart Radio Station Launcher
echo.
echo ========================================
echo    🎵 SMART RADIO STATION LAUNCHER 🎵
echo ========================================
echo.
echo Starting Smart Radio Station...
echo.

REM Change to the project directory
cd /d "D:\practice"

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js detected
echo.

REM Check if server is already running
netstat -an | find "5002" | find "LISTENING" >nul
if errorlevel 1 (
    echo 🚀 Starting Radio Server...
    start /b "Radio Server" cmd /c "set PORT=5002 && node server/index.js"
    timeout /t 3 /nobreak >nul
    echo ✅ Server started on http://localhost:5002
) else (
    echo ✅ Server already running on http://localhost:5002
)
echo.

REM Check if client is already running
netstat -an | find "3000" | find "LISTENING" >nul
if errorlevel 1 (
    echo 🎵 Starting Radio Dashboard...
    cd client
    start /b "Radio Client" cmd /c "npm start"
    echo ⏳ Waiting for dashboard to load...
    timeout /t 10 /nobreak >nul
    echo ✅ Dashboard started
) else (
    echo ✅ Dashboard already running
)
echo.

REM Determine which port the client is using
netstat -an | find "3000" | find "LISTENING" >nul
if errorlevel 1 (
    netstat -an | find "3001" | find "LISTENING" >nul
    if errorlevel 1 (
        set CLIENT_URL=http://localhost:3000
    ) else (
        set CLIENT_URL=http://localhost:3001
    )
) else (
    set CLIENT_URL=http://localhost:3000
)

REM Open the browser
echo 🌐 Opening Smart Radio Station in browser...
start "" "%CLIENT_URL%"

echo.
echo ========================================
echo    🎉 SMART RADIO STATION IS READY! 🎉
echo ========================================
echo.
echo 📡 Server: http://localhost:5002
echo 🎵 Dashboard: %CLIENT_URL%
echo.
echo Press any key to close this launcher...
pause >nul
