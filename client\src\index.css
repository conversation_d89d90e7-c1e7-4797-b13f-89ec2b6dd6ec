@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn-primary {
    @apply bg-radio-600 hover:bg-radio-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6 border border-gray-200;
  }

  /* Volume Slider Styles */
  .slider {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
  }

  .slider::-webkit-slider-track {
    background: #e5e7eb;
    height: 4px;
    border-radius: 2px;
  }

  .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: #3b82f6;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    cursor: pointer;
  }

  .slider::-moz-range-track {
    background: #e5e7eb;
    height: 4px;
    border-radius: 2px;
    border: none;
  }

  .slider::-moz-range-thumb {
    background: #3b82f6;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    cursor: pointer;
    border: none;
  }

  /* Vertical Slider Styles for DJ EQ */
  .vertical-slider {
    writing-mode: bt-lr; /* IE */
    -webkit-appearance: slider-vertical; /* WebKit */
    width: 20px;
    height: 80px;
    background: transparent;
    outline: none;
  }

  .vertical-slider::-webkit-slider-track {
    background: #374151;
    width: 4px;
    border-radius: 2px;
  }

  .vertical-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: #3b82f6;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
  }

  /* DJ Turntable Animations */
  @keyframes vinyl-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .vinyl-spinning {
    animation: vinyl-spin 2s linear infinite;
  }

  @keyframes scratch-effect {
    0% { transform: scaleX(1); }
    50% { transform: scaleX(1.1); }
    100% { transform: scaleX(1); }
  }

  .scratch-animation {
    animation: scratch-effect 0.1s ease-in-out;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-radio-500 focus:border-transparent;
  }
  
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }
  
  .nav-link-active {
    @apply bg-radio-100 text-radio-700;
  }
  
  .nav-link-inactive {
    @apply text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Audio visualizer styles */
.audio-visualizer {
  display: flex;
  align-items: end;
  height: 40px;
  gap: 2px;
}

.audio-bar {
  width: 3px;
  background: linear-gradient(to top, #0ea5e9, #38bdf8);
  border-radius: 1px;
  animation: audio-bounce 1s ease-in-out infinite alternate;
}

.audio-bar:nth-child(2) { animation-delay: 0.1s; }
.audio-bar:nth-child(3) { animation-delay: 0.2s; }
.audio-bar:nth-child(4) { animation-delay: 0.3s; }
.audio-bar:nth-child(5) { animation-delay: 0.4s; }

@keyframes audio-bounce {
  0% { height: 10px; }
  100% { height: 40px; }
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0ea5e9;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* DJ Player Custom Styles */
.slider {
  background: linear-gradient(to right, #3b82f6 0%, #3b82f6 var(--value, 0%), #374151 var(--value, 0%), #374151 100%);
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.crossfader {
  background: linear-gradient(to right, #ef4444 0%, #ef4444 25%, #22c55e 50%, #3b82f6 75%, #3b82f6 100%);
}

.crossfader::-webkit-slider-thumb {
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: #ffffff;
  cursor: pointer;
  border: 2px solid #000000;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.crossfader::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: #ffffff;
  cursor: pointer;
  border: 2px solid #000000;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* Waveform Animation */
@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}
