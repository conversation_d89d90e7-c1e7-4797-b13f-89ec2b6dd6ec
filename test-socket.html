<!DOCTYPE html>
<html>
<head>
    <title>Socket.IO Test</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
</head>
<body>
    <h1>Socket.IO Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="logs"></div>

    <script>
        const socket = io('http://localhost:5002', {
            transports: ['websocket', 'polling'],
            timeout: 20000,
            forceNew: true
        });

        const statusDiv = document.getElementById('status');
        const logsDiv = document.getElementById('logs');

        function addLog(message) {
            const p = document.createElement('p');
            p.textContent = new Date().toLocaleTimeString() + ': ' + message;
            logsDiv.appendChild(p);
            console.log(message);
        }

        socket.on('connect', () => {
            statusDiv.textContent = '✅ Connected to server';
            statusDiv.style.color = 'green';
            addLog('Connected to server with ID: ' + socket.id);
        });

        socket.on('disconnect', (reason) => {
            statusDiv.textContent = '❌ Disconnected from server';
            statusDiv.style.color = 'red';
            addLog('Disconnected: ' + reason);
        });

        socket.on('connect_error', (error) => {
            statusDiv.textContent = '❌ Connection error';
            statusDiv.style.color = 'red';
            addLog('Connection error: ' + error.message);
        });

        socket.on('status', (data) => {
            addLog('Received status: ' + JSON.stringify(data));
        });

        addLog('Attempting to connect to http://localhost:5002');
    </script>
</body>
</html>
