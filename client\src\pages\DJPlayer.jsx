import React, { useState, useRef, useEffect } from 'react';
import { useRadio } from '../context/RadioContext';
import { 
  Play, 
  Pause, 
  SkipForward, 
  SkipBack,
  Volume2,
  Shuffle,
  Repeat,
  Music,
  Settings,
  Mic,
  Radio,
  Upload,
  List,
  Search,
  Filter,
  Clock,
  User,
  Disc
} from 'lucide-react';

export default function DJPlayer() {
  const { state, api } = useRadio();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTrack, setSelectedTrack] = useState(null);
  const [crossfaderPosition, setCrossfaderPosition] = useState(50);
  const [selectedTracks, setSelectedTracks] = useState(new Set());
  const [showPlaylistModal, setShowPlaylistModal] = useState(false);
  const [newPlaylistName, setNewPlaylistName] = useState('');
  const fileInputRef = useRef(null);

  const filteredTracks = state.tracks.filter(track =>
    track.original_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    track.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    track.artist?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDuration = (seconds) => {
    if (!seconds) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return '0 KB';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    try {
      for (const file of files) {
        await api.uploadTrack(file);
      }
    } catch (error) {
      console.error('Upload failed:', error);
      alert('Upload failed: ' + error.message);
    } finally {
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handlePlayTrack = async (track) => {
    try {
      // First add to queue, then play
      await api.addToQueue(track);
      await api.playTrack(track);
      setSelectedTrack(track);
    } catch (error) {
      console.error('Failed to play track:', error);
      alert('Failed to play track: ' + error.message);
    }
  };

  const handleTrackSelect = (track, isCtrlClick = false) => {
    if (isCtrlClick) {
      // Multi-select with Ctrl+Click
      setSelectedTracks(prev => {
        const newSet = new Set(prev);
        if (newSet.has(track.id)) {
          newSet.delete(track.id);
        } else {
          newSet.add(track.id);
        }
        return newSet;
      });
    } else {
      // Single select or play
      handlePlayTrack(track);
    }
  };

  const createPlaylist = async () => {
    if (!newPlaylistName.trim()) {
      alert('Please enter a playlist name');
      return;
    }

    if (selectedTracks.size === 0) {
      alert('Please select tracks for the playlist');
      return;
    }

    try {
      const trackIds = Array.from(selectedTracks);
      await api.createPlaylist({
        name: newPlaylistName,
        trackIds: trackIds
      });

      // Reset form
      setNewPlaylistName('');
      setSelectedTracks(new Set());
      setShowPlaylistModal(false);

      alert(`Playlist "${newPlaylistName}" created successfully!`);
    } catch (error) {
      console.error('Failed to create playlist:', error);
      alert('Failed to create playlist: ' + error.message);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white">
      {/* Top Control Bar */}
      <div className="bg-black/50 backdrop-blur-sm border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          {/* Left Controls */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Radio className="w-6 h-6 text-blue-400" />
              <span className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                OTS-Style DJ Player
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
              >
                <Upload className="w-4 h-4" />
                <span>Load</span>
              </button>
              
              <button className="flex items-center space-x-2 px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                <Settings className="w-4 h-4" />
                <span>Config</span>
              </button>
              
              <button className="flex items-center space-x-2 px-3 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                <Mic className="w-4 h-4" />
                <span>Live</span>
              </button>

              <button
                onClick={() => setShowPlaylistModal(true)}
                className="flex items-center space-x-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
                disabled={selectedTracks.size === 0}
              >
                <List className="w-4 h-4" />
                <span>Create Playlist ({selectedTracks.size})</span>
              </button>
            </div>
          </div>

          {/* Right Status */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${state.isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
              <span className="text-sm">{state.isConnected ? 'Connected' : 'Disconnected'}</span>
            </div>
            
            <div className="text-sm">
              <span className="text-gray-400">Tracks: </span>
              <span className="text-white">{state.tracks.length}</span>
            </div>
            
            <div className="text-sm">
              <span className="text-gray-400">Queue: </span>
              <span className="text-white">{state.queue.length}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="flex h-[calc(100vh-80px)]">
        {/* Left Panel - Deck A */}
        <div className="w-1/4 bg-black/30 backdrop-blur-sm border-r border-gray-700 p-4">
          <div className="bg-gradient-to-br from-blue-900/50 to-purple-900/50 rounded-lg p-4 h-full">
            <div className="text-center mb-4">
              <div className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-bold mb-2">
                DECK A • PLAYING
              </div>
            </div>

            {/* Deck A Waveform */}
            <div className="bg-black/50 rounded-lg p-4 mb-4 h-32 flex items-center justify-center">
              <div className="w-full h-16 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded flex items-center justify-center">
                <div className="text-blue-400 animate-pulse">
                  <Music className="w-8 h-8" />
                </div>
              </div>
            </div>

            {/* Current Track Info */}
            <div className="bg-black/30 rounded-lg p-3 mb-4">
              <div className="text-sm text-gray-400 mb-1">Now Playing:</div>
              <div className="font-medium text-white truncate">
                {state.currentTrack?.originalName || 'No track loaded'}
              </div>
              <div className="text-xs text-gray-400 mt-1">
                {state.currentTrack?.artist || 'Unknown Artist'}
              </div>
              <div className="text-xs text-gray-400">
                {formatDuration(state.currentTrack?.duration)}
              </div>
            </div>

            {/* Deck A Controls */}
            <div className="space-y-3">
              <div className="flex justify-center space-x-2">
                <button
                  onClick={() => api.previous()}
                  className="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                  disabled={state.loading}
                >
                  <SkipBack className="w-5 h-5" />
                </button>

                <button
                  onClick={() => {
                    if (state.isPlaying) {
                      api.pause();
                    } else {
                      api.play();
                    }
                  }}
                  className="p-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50"
                  disabled={state.loading}
                >
                  {state.isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                </button>

                <button
                  onClick={() => api.next()}
                  className="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                  disabled={state.loading}
                >
                  <SkipForward className="w-5 h-5" />
                </button>
              </div>

              {/* Volume Control */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-400">Volume</span>
                  <span className="text-xs text-white">{Math.round(state.volume * 100)}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={state.volume}
                  onChange={(e) => api.setVolume(parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>

              {/* Deck A Options */}
              <div className="flex justify-center space-x-2">
                <button className="p-2 bg-gray-700 hover:bg-gray-600 rounded transition-colors">
                  <Shuffle className="w-4 h-4" />
                </button>
                <button className="p-2 bg-gray-700 hover:bg-gray-600 rounded transition-colors">
                  <Repeat className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Center Panel - Main Waveform & Crossfader */}
        <div className="flex-1 bg-black/20 backdrop-blur-sm p-4">
          {/* Main Waveform Display */}
          <div className="bg-black/50 rounded-lg p-4 mb-4 h-32">
            <div className="w-full h-full bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded flex items-center justify-center relative overflow-hidden">
              {/* Simulated Waveform */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-full h-16 flex items-end justify-center space-x-1">
                  {Array.from({ length: 100 }).map((_, i) => (
                    <div
                      key={i}
                      className="bg-gradient-to-t from-blue-500 to-purple-500 opacity-60"
                      style={{
                        width: '2px',
                        height: `${Math.random() * 60 + 10}px`,
                        animation: `pulse ${Math.random() * 2 + 1}s infinite`
                      }}
                    />
                  ))}
                </div>
              </div>
              
              {/* Playhead */}
              <div className="absolute top-0 left-1/2 w-0.5 h-full bg-red-500 transform -translate-x-1/2"></div>
            </div>
          </div>

          {/* Crossfader */}
          <div className="bg-black/30 rounded-lg p-4 mb-4">
            <div className="text-center mb-2">
              <span className="text-sm text-gray-400">CROSSFADER</span>
            </div>
            <div className="relative">
              <input
                type="range"
                min="0"
                max="100"
                value={crossfaderPosition}
                onChange={(e) => setCrossfaderPosition(e.target.value)}
                className="w-full h-3 bg-gray-700 rounded-lg appearance-none cursor-pointer crossfader"
              />
              <div className="flex justify-between text-xs text-gray-400 mt-1">
                <span>A</span>
                <span>B</span>
              </div>
            </div>
          </div>

          {/* Transport Controls */}
          <div className="bg-black/30 rounded-lg p-4">
            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={() => api.previous()}
                className="p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                disabled={state.loading}
              >
                <SkipBack className="w-6 h-6" />
              </button>

              <button
                onClick={() => {
                  if (state.isPlaying) {
                    api.pause();
                  } else {
                    api.play();
                  }
                }}
                className="p-4 bg-green-600 hover:bg-green-700 rounded-lg transition-colors disabled:opacity-50"
                disabled={state.loading}
              >
                {state.isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
              </button>

              <button
                onClick={() => api.next()}
                className="p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                disabled={state.loading}
              >
                <SkipForward className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>

        {/* Right Panel - Playlist */}
        <div className="w-2/5 bg-black/30 backdrop-blur-sm border-l border-gray-700 p-4">
          <div className="h-full flex flex-col">
            {/* Search */}
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search tracks, artists, albums..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-black/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                />
              </div>

              {/* Instructions */}
              <div className="mt-2 text-xs text-gray-400">
                💡 <strong>Click</strong> to play • <strong>Ctrl+Click</strong> to select for playlist
              </div>
            </div>

            {/* Playlist Header */}
            <div className="bg-black/50 rounded-lg p-2 mb-2">
              <div className="grid grid-cols-12 gap-2 text-xs text-gray-400 font-medium">
                <div className="col-span-1">#</div>
                <div className="col-span-5">Title</div>
                <div className="col-span-3">Artist</div>
                <div className="col-span-2">Length</div>
                <div className="col-span-1">BPM</div>
              </div>
            </div>

            {/* Playlist */}
            <div className="flex-1 overflow-y-auto space-y-1">
              {filteredTracks.map((track, index) => (
                <div
                  key={track.id}
                  onClick={(e) => handleTrackSelect(track, e.ctrlKey)}
                  className={`grid grid-cols-12 gap-2 p-2 rounded cursor-pointer transition-colors text-sm ${
                    selectedTracks.has(track.id)
                      ? 'bg-purple-600/50 text-white border border-purple-400'
                      : selectedTrack?.id === track.id
                      ? 'bg-blue-600/50 text-white'
                      : 'bg-black/30 hover:bg-black/50 text-gray-300'
                  }`}
                >
                  <div className="col-span-1 text-gray-400">{index + 1}</div>
                  <div className="col-span-5 truncate font-medium">
                    {track.originalName || track.title}
                  </div>
                  <div className="col-span-3 truncate text-gray-400">
                    {track.artist || 'Unknown'}
                  </div>
                  <div className="col-span-2 text-gray-400">
                    {formatDuration(track.duration)}
                  </div>
                  <div className="col-span-1 text-gray-400">
                    {track.bpm || '--'}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="audio/*"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* Playlist Creation Modal */}
      {showPlaylistModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-lg p-6 w-96 border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-4">Create New Playlist</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm text-gray-400 mb-2">Playlist Name</label>
                <input
                  type="text"
                  value={newPlaylistName}
                  onChange={(e) => setNewPlaylistName(e.target.value)}
                  placeholder="Enter playlist name..."
                  className="w-full px-3 py-2 bg-black/50 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                />
              </div>

              <div>
                <label className="block text-sm text-gray-400 mb-2">
                  Selected Tracks ({selectedTracks.size})
                </label>
                <div className="max-h-32 overflow-y-auto bg-black/30 rounded p-2">
                  {Array.from(selectedTracks).map(trackId => {
                    const track = state.tracks.find(t => t.id === trackId);
                    return track ? (
                      <div key={trackId} className="text-sm text-gray-300 py-1">
                        • {track.originalName || track.title}
                      </div>
                    ) : null;
                  })}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowPlaylistModal(false);
                  setNewPlaylistName('');
                }}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={createPlaylist}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded transition-colors"
              >
                Create Playlist
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
