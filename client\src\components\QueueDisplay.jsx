import { useState } from 'react';
import { useRadio } from '../context/RadioContext';
import { 
  List, 
  Play, 
  Trash2, 
  Music,
  Clock,
  ChevronUp,
  ChevronDown
} from 'lucide-react';

const QueueDisplay = () => {
  const { state } = useRadio();
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDuration = (seconds) => {
    if (!seconds) return '--:--';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return '--';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  if (!state.queue || state.queue.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
              <List className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-900">Playback Queue</h2>
              <p className="text-sm text-gray-600">No tracks in queue</p>
            </div>
          </div>
        </div>
        
        <div className="text-center py-8">
          <Music className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500 mb-2">Queue is empty</p>
          <p className="text-sm text-gray-400">Add tracks from Music Library to start playing</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
            <List className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-gray-900">Playback Queue</h2>
            <p className="text-sm text-gray-600">
              {state.queue.length} track{state.queue.length !== 1 ? 's' : ''} in queue
            </p>
          </div>
        </div>
        
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <span>{isExpanded ? 'Collapse' : 'Expand'}</span>
          {isExpanded ? (
            <ChevronUp className="w-4 h-4" />
          ) : (
            <ChevronDown className="w-4 h-4" />
          )}
        </button>
      </div>

      {/* Queue Summary */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="bg-gray-50 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-gray-900">{state.queue.length}</div>
          <div className="text-xs text-gray-600">Tracks</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-gray-900">
            {formatDuration(state.queue.reduce((total, track) => total + (track.duration || 0), 0))}
          </div>
          <div className="text-xs text-gray-600">Total Time</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-gray-900">
            {formatFileSize(state.queue.reduce((total, track) => total + (track.size || 0), 0))}
          </div>
          <div className="text-xs text-gray-600">Total Size</div>
        </div>
      </div>

      {/* Current Track */}
      {state.currentTrack && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4 mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <Play className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-gray-900">Now Playing</h4>
              <p className="text-sm text-gray-600 truncate">{state.currentTrack.originalName}</p>
            </div>
            <div className="text-sm text-gray-500">
              {formatDuration(state.currentTrack.duration)}
            </div>
          </div>
        </div>
      )}

      {/* Queue List */}
      {isExpanded && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Up Next:</h4>
          <div className="max-h-64 overflow-y-auto space-y-2">
            {state.queue.map((track, index) => (
              <div
                key={`${track.id}-${index}`}
                className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="w-6 h-6 bg-gray-300 rounded text-xs flex items-center justify-center text-gray-600 font-medium">
                  {index + 1}
                </div>
                
                <div className="w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-600 rounded flex items-center justify-center">
                  <Music className="w-4 h-4 text-white" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {track.originalName}
                  </p>
                  <p className="text-xs text-gray-500">
                    {track.artist || 'Unknown Artist'} • {formatDuration(track.duration)}
                  </p>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500">
                    {formatFileSize(track.size)}
                  </span>
                  <button
                    className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                    title="Remove from queue"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-600">
              Estimated time: {formatDuration(state.queue.reduce((total, track) => total + (track.duration || 0), 0))}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <button className="px-3 py-1 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors">
              Shuffle
            </button>
            <button className="px-3 py-1 text-xs text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors">
              Clear All
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QueueDisplay;
