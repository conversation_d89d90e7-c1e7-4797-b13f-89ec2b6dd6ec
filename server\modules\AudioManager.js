const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const EventEmitter = require('events');

class AudioManager extends EventEmitter {
  constructor(io) {
    super();
    this.io = io;
    this.currentTrack = null;
    this.isPlayingState = false;
    this.volume = 0.8;
    this.queue = [];
    this.currentIndex = 0;
    this.ffmpegProcess = null;
    this.crossfadeDuration = 3000; // 3 seconds
    this.autoPlay = false; // Disable auto-play to prevent rapid switching
    
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    this.on('trackEnded', () => {
      if (this.autoPlay) {
        this.next();
      }
    });

    this.on('trackStarted', (track) => {
      this.io.emit('trackChanged', {
        track,
        isPlaying: this.isPlayingState,
        queue: this.queue,
        currentIndex: this.currentIndex
      });
    });

    this.on('playStateChanged', (isPlaying) => {
      this.io.emit('playStateChanged', { isPlaying });
    });
  }

  async loadQueue(tracks) {
    this.queue = [...tracks];
    this.currentIndex = 0;
    
    if (this.queue.length > 0 && this.autoPlay) {
      await this.loadTrack(this.queue[0]);
    }
  }

  async addToQueue(track) {
    this.queue.push(track);
    console.log('Track added to queue:', track.originalName, 'Queue length:', this.queue.length);

    if (!this.currentTrack && this.autoPlay) {
      console.log('No current track, loading and playing:', track.originalName);
      await this.loadTrack(track);
      // Auto-start playback
      setTimeout(() => this.play(), 500);
    }
  }

  async loadTrack(track) {
    try {
      // Prevent loading the same track multiple times
      if (this.currentTrack && this.currentTrack.id === track.id) {
        console.log('Track already loaded:', track.originalName);
        return;
      }

      if (this.ffmpegProcess) {
        this.ffmpegProcess.kill();
        this.ffmpegProcess = null;
      }

      this.currentTrack = track;

      // Verify file exists
      if (!fs.existsSync(track.path)) {
        console.error('Track file not found:', track.path);
        this.next();
        return;
      }

      console.log('Loading track:', track.originalName);
      this.emit('trackStarted', track);

    } catch (error) {
      console.error('Error loading track:', error);
      this.next();
    }
  }

  play() {
    if (!this.currentTrack) {
      if (this.queue.length > 0) {
        this.loadTrack(this.queue[this.currentIndex]);
      }
      return;
    }

    try {
      // Kill existing process
      if (this.ffmpegProcess) {
        this.ffmpegProcess.kill();
      }

      // Start FFmpeg process for audio streaming to web clients
      const ffmpegPath = process.env.FFMPEG_PATH || 'ffmpeg';

      console.log('Starting audio stream for:', this.currentTrack.originalName);

      // Stream audio data for web playback
      this.ffmpegProcess = spawn(ffmpegPath, [
        '-i', this.currentTrack.path,
        '-f', 'mp3',
        '-ab', '128k',
        '-ar', '44100',
        '-ac', '2',
        '-filter:a', `volume=${this.volume}`,
        'pipe:1'  // Output to stdout for streaming
      ]);

      this.ffmpegProcess.stdout.on('data', (data) => {
        // Audio data output (not used for direct playback)
      });

      this.ffmpegProcess.stderr.on('data', (data) => {
        const log = data.toString();
        if (log.includes('time=')) {
          // Progress information
          console.log('Playback progress:', log.trim());
        } else if (log.includes('error') || log.includes('Error')) {
          console.error('FFmpeg error:', log.trim());
        }
      });

      this.ffmpegProcess.on('close', (code) => {
        if (code === 0) {
          console.log('Track finished playing');
          this.emit('trackEnded');
        }
      });

      this.ffmpegProcess.on('error', (error) => {
        console.error('FFmpeg error:', error);
        this.next();
      });

      this.isPlayingState = true;
      this.emit('playStateChanged', true);
      
    } catch (error) {
      console.error('Error starting playback:', error);
    }
  }

  pause() {
    if (this.ffmpegProcess) {
      // On Windows, we need to kill and restart instead of pause/resume
      this.ffmpegProcess.kill();
      this.ffmpegProcess = null;
    }
    this.isPlayingState = false;
    this.emit('playStateChanged', false);
    console.log('Playback paused');
  }

  resume() {
    // On Windows, restart the current track
    if (this.currentTrack && !this.isPlayingState) {
      this.play();
    }
  }

  stop() {
    if (this.ffmpegProcess) {
      this.ffmpegProcess.kill();
      this.ffmpegProcess = null;
    }
    this.isPlayingState = false;
    this.emit('playStateChanged', false);
  }

  next() {
    if (this.queue.length === 0) return;
    
    this.currentIndex = (this.currentIndex + 1) % this.queue.length;
    this.loadTrack(this.queue[this.currentIndex]);
    
    if (this.isPlayingState) {
      setTimeout(() => this.play(), 100);
    }
  }

  previous() {
    if (this.queue.length === 0) return;
    
    this.currentIndex = this.currentIndex === 0 ? this.queue.length - 1 : this.currentIndex - 1;
    this.loadTrack(this.queue[this.currentIndex]);
    
    if (this.isPlayingState) {
      setTimeout(() => this.play(), 100);
    }
  }

  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
    
    // If currently playing, restart with new volume
    if (this.isPlayingState && this.currentTrack) {
      this.play();
    }
  }

  shuffle() {
    for (let i = this.queue.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [this.queue[i], this.queue[j]] = [this.queue[j], this.queue[i]];
    }
    this.currentIndex = 0;
  }

  // Getters
  getCurrentTrack() {
    return this.currentTrack;
  }

  isPlaying() {
    return this.isPlayingState;
  }

  getVolume() {
    return this.volume;
  }

  getQueue() {
    return this.queue;
  }

  getCurrentIndex() {
    return this.currentIndex;
  }

  clearQueue() {
    this.queue = [];
    this.currentIndex = 0;
    console.log('Queue cleared');
  }

  // Advanced features
  async crossfade(nextTrack) {
    // Implementation for smooth crossfading between tracks
    // This would require more complex audio processing
    console.log('Crossfading to:', nextTrack.originalName);
    
    // For now, just do a quick transition
    this.loadTrack(nextTrack);
    if (this.isPlayingState) {
      setTimeout(() => this.play(), this.crossfadeDuration / 10);
    }
  }

  setAutoPlay(enabled) {
    this.autoPlay = enabled;
  }

  setCrossfadeDuration(duration) {
    this.crossfadeDuration = duration;
  }

  // Get audio metadata
  async getTrackDuration(trackPath) {
    return new Promise((resolve, reject) => {
      const ffprobePath = process.env.FFPROBE_PATH || 'ffprobe';
      const ffprobe = spawn(ffprobePath, [
        '-v', 'quiet',
        '-print_format', 'json',
        '-show_format',
        trackPath
      ]);

      let output = '';
      ffprobe.stdout.on('data', (data) => {
        output += data.toString();
      });

      ffprobe.on('close', (code) => {
        if (code === 0) {
          try {
            const metadata = JSON.parse(output);
            resolve(parseFloat(metadata.format.duration));
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new Error('FFprobe failed'));
        }
      });
    });
  }

  // Audio streaming for web clients
  createAudioStream() {
    // This would create a web-compatible audio stream
    // Implementation would depend on the streaming protocol chosen
    console.log('Creating audio stream for web clients');
  }
}

module.exports = AudioManager;
