const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const cron = require('node-cron');
require('dotenv').config();

// Import our custom modules
const AudioManager = require('./modules/AudioManager');
const AdManager = require('./modules/AdManager');
const PlaylistManager = require('./modules/PlaylistManager');
const ScheduleManager = require('./modules/ScheduleManager');
const DatabaseManager = require('./modules/DatabaseManager');
const StreamingManager = require('./modules/StreamingManager');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: [
      process.env.CLIENT_URL || "http://localhost:3000",
      "http://localhost:3001",
      "http://localhost:5002"
    ],
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 5001;

// Middleware
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:5001',
    'http://localhost:5002'
  ],
  credentials: true
}));
app.use(express.json({ limit: '50mb' }));
app.use(express.static(path.join(__dirname, '../client/dist')));

// File upload configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    console.log('File filter check:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      fieldname: file.fieldname
    });

    const allowedExtensions = /\.(mp3|wav|flac|m4a|aac)$/i;
    const allowedMimeTypes = /^audio\//i;

    const extname = allowedExtensions.test(file.originalname);
    const mimetype = allowedMimeTypes.test(file.mimetype) || file.mimetype === 'application/octet-stream';

    console.log('Filter results:', {
      extname,
      mimetype,
      originalMimeType: file.mimetype,
      filename: file.originalname
    });

    // Accept if either extension is valid OR mimetype is audio-related
    if (extname || mimetype) {
      console.log('File accepted:', file.originalname);
      return cb(null, true);
    } else {
      console.log('File rejected:', file.originalname, 'Extension check:', extname, 'MIME check:', mimetype);
      cb(new Error('Only audio files are allowed! Supported formats: MP3, WAV, FLAC, M4A, AAC'));
    }
  },
  limits: { fileSize: 50 * 1024 * 1024 } // 50MB limit
});

// Initialize managers
const dbManager = new DatabaseManager();
const audioManager = new AudioManager(io);
const adManager = new AdManager(io);
const playlistManager = new PlaylistManager(dbManager);
const streamingManager = new StreamingManager(audioManager, {
  port: process.env.STREAMING_PORT || 8000,
  bitrate: 128,
  format: 'mp3'
});
let scheduleManager;

// API Routes
app.get('/api/status', (req, res) => {
  res.json({
    status: 'running',
    currentTrack: audioManager.getCurrentTrack(),
    isPlaying: audioManager.isPlaying(),
    volume: audioManager.getVolume(),
    queue: audioManager.getQueue(),
    uptime: process.uptime()
  });
});

app.post('/api/upload/music', upload.single('audio'), async (req, res) => {
  try {
    console.log('Upload request received:', {
      file: req.file ? 'File present' : 'No file',
      body: req.body,
      headers: req.headers['content-type']
    });

    if (!req.file) {
      console.log('No file in request');
      return res.status(400).json({ error: 'No file uploaded' });
    }

    console.log('File details:', {
      filename: req.file.filename,
      originalname: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
    });

    const trackData = {
      id: uuidv4(),
      filename: req.file.filename,
      originalName: req.file.originalname,
      path: req.file.path,
      size: req.file.size,
      uploadedAt: new Date().toISOString(),
      metadata: req.body
    };

    await playlistManager.addTrack(trackData);
    console.log('Track added successfully:', trackData.id);

    // Add track to audio queue for immediate playback
    await audioManager.addToQueue(trackData);
    console.log('Track added to playback queue:', trackData.originalName);

    res.json({ success: true, track: trackData });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed', details: error.message });
  }
});

app.post('/api/upload/ad', upload.single('audio'), async (req, res) => {
  try {
    console.log('Ad upload request received:', {
      file: req.file ? 'File present' : 'No file',
      body: req.body,
      headers: req.headers['content-type']
    });

    if (!req.file) {
      console.log('No ad file in request');
      return res.status(400).json({ error: 'No file uploaded' });
    }

    console.log('Ad file details:', {
      filename: req.file.filename,
      originalname: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
    });

    const adData = {
      id: uuidv4(),
      filename: req.file.filename,
      originalName: req.file.originalname,
      path: req.file.path,
      size: req.file.size,
      uploadedAt: new Date().toISOString(),
      metadata: req.body
    };

    await adManager.addAd(adData);
    console.log('Ad added successfully:', adData.id);
    res.json({ success: true, ad: adData });
  } catch (error) {
    console.error('Ad upload error:', error);
    res.status(500).json({ error: 'Ad upload failed', details: error.message });
  }
});

app.get('/api/playlist', async (req, res) => {
  try {
    const tracks = await playlistManager.getAllTracks();
    res.json(tracks);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch playlist' });
  }
});

app.get('/api/ads', async (req, res) => {
  try {
    const ads = await adManager.getAllAds();
    res.json(ads);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch ads' });
  }
});

app.post('/api/control/play', (req, res) => {
  audioManager.play();
  res.json({ success: true });
});

app.post('/api/control/pause', (req, res) => {
  audioManager.pause();
  res.json({ success: true });
});

app.post('/api/control/next', (req, res) => {
  audioManager.next();
  res.json({ success: true });
});

app.post('/api/control/volume', (req, res) => {
  const { volume } = req.body;
  audioManager.setVolume(volume);
  res.json({ success: true, volume });
});

// Queue management endpoints
app.post('/api/queue/add', async (req, res) => {
  try {
    const { trackId } = req.body;
    const track = await playlistManager.getTrackById(trackId);
    if (!track) {
      return res.status(404).json({ error: 'Track not found' });
    }

    await audioManager.addToQueue(track);
    console.log('Track added to queue:', track.originalName);
    res.json({ success: true, track });
  } catch (error) {
    console.error('Add to queue error:', error);
    res.status(500).json({ error: 'Failed to add track to queue' });
  }
});

app.post('/api/control/play-track', async (req, res) => {
  try {
    const { trackId } = req.body;
    const track = await playlistManager.getTrackById(trackId);
    if (!track) {
      return res.status(404).json({ error: 'Track not found' });
    }

    // Clear current queue and add this track
    audioManager.clearQueue();
    await audioManager.addToQueue(track);
    audioManager.play();

    console.log('Playing track immediately:', track.originalName);
    res.json({ success: true, track });
  } catch (error) {
    console.error('Play track error:', error);
    res.status(500).json({ error: 'Failed to play track' });
  }
});

// Streaming endpoints
app.get('/api/stream/info', (req, res) => {
  res.json(streamingManager.getStreamInfo());
});

app.post('/api/stream/start', (req, res) => {
  try {
    streamingManager.startStreaming();
    res.json({ success: true, message: 'Streaming started' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/stream/stop', (req, res) => {
  try {
    streamingManager.stopStreaming();
    res.json({ success: true, message: 'Streaming stopped' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Audio streaming endpoint for web player
app.get('/api/audio/current', (req, res) => {
  // Check if an ad is currently playing
  const currentAd = adManager.getCurrentAd();
  if (currentAd) {
    console.log('Streaming current ad to web player:', currentAd.originalName);
    res.setHeader('Content-Type', 'audio/mpeg');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Access-Control-Allow-Origin', '*');

    const fs = require('fs');
    const adStream = fs.createReadStream(currentAd.path);
    adStream.pipe(res);
    return;
  }

  const currentTrack = audioManager.getCurrentTrack();
  if (!currentTrack) {
    return res.status(404).json({ error: 'No track currently playing' });
  }

  console.log('Streaming current track to web player:', currentTrack.originalName);
  res.setHeader('Content-Type', 'audio/mpeg');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Access-Control-Allow-Origin', '*');

  // Stream the current track file directly
  const fs = require('fs');
  const trackStream = fs.createReadStream(currentTrack.path);
  trackStream.pipe(res);
});

// Playlist management endpoints
app.post('/api/playlists', async (req, res) => {
  try {
    const { name, trackIds, tracks, createdAt, totalTracks, totalDuration } = req.body;

    const result = await dbManager.db.run(
      `INSERT INTO playlists (name, track_ids, tracks_data, created_at, total_tracks, total_duration)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [
        name,
        JSON.stringify(trackIds || []),
        JSON.stringify(tracks || []),
        createdAt || new Date().toISOString(),
        totalTracks || 0,
        totalDuration || 0
      ]
    );

    res.json({
      success: true,
      playlistId: result.lastID,
      message: `Playlist "${name}" created successfully`
    });
  } catch (error) {
    console.error('Error creating playlist:', error);
    res.status(500).json({ error: 'Failed to create playlist' });
  }
});

app.get('/api/playlists', async (req, res) => {
  try {
    const playlists = await dbManager.db.all(`
      SELECT id, name, created_at, total_tracks, total_duration
      FROM playlists
      ORDER BY created_at DESC
    `);

    res.json(playlists);
  } catch (error) {
    console.error('Error fetching playlists:', error);
    res.status(500).json({ error: 'Failed to fetch playlists' });
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  // Send current status to new client
  socket.emit('status', {
    currentTrack: audioManager.getCurrentTrack(),
    isPlaying: audioManager.isPlaying(),
    volume: audioManager.getVolume(),
    queue: audioManager.getQueue()
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Schedule management - run every minute
cron.schedule('* * * * *', () => {
  if (scheduleManager) {
    scheduleManager.checkSchedule();
  }
});

// Serve React app for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

// Initialize database and start server
async function startServer() {
  try {
    await dbManager.initialize();
    console.log('Database initialized');

    // Initialize schedule manager after database is ready
    scheduleManager = new ScheduleManager(dbManager, audioManager, adManager);

    server.listen(PORT, () => {
      console.log(`🎵 Smart Radio Station running on port ${PORT}`);
      console.log(`📡 Dashboard: http://localhost:${PORT}`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
