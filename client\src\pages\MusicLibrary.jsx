import React, { useState, useRef } from 'react';
import { useRadio } from '../context/RadioContext';
import { 
  Upload, 
  Music, 
  Search, 
  Play, 
  Plus,
  MoreVertical,
  Clock,
  User,
  Disc
} from 'lucide-react';

export default function MusicLibrary() {
  const { state, api } = useRadio();
  const [searchTerm, setSearchTerm] = useState('');
  const [uploading, setUploading] = useState(false);
  const [actionLoading, setActionLoading] = useState({});
  const fileInputRef = useRef(null);

  const filteredTracks = state.tracks.filter(track =>
    track.original_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    track.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    track.artist?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    track.album?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleFileUpload = async (event) => {
    console.log('File upload triggered');
    const files = Array.from(event.target.files);
    console.log('Selected files:', files);

    if (files.length === 0) {
      console.log('No files selected');
      return;
    }

    setUploading(true);
    try {
      for (const file of files) {
        console.log('Uploading file:', file.name, file.type, file.size);
        await api.uploadTrack(file);
        console.log('File uploaded successfully:', file.name);
      }
    } catch (error) {
      console.error('Upload failed:', error);
      alert('Upload failed: ' + error.message);
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleAddToQueue = async (track) => {
    setActionLoading(prev => ({ ...prev, [`queue-${track.id}`]: true }));
    try {
      await api.addToQueue(track);
      // Show success notification
      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
      notification.textContent = `"${track.originalName}" added to queue!`;
      document.body.appendChild(notification);
      setTimeout(() => document.body.removeChild(notification), 3000);
    } catch (error) {
      console.error('Failed to add to queue:', error);
      alert('Failed to add track to queue: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [`queue-${track.id}`]: false }));
    }
  };

  const handlePlayNow = async (track) => {
    setActionLoading(prev => ({ ...prev, [`play-${track.id}`]: true }));
    try {
      await api.playTrack(track);
      // Show success notification
      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
      notification.textContent = `Now playing: "${track.originalName}"`;
      document.body.appendChild(notification);
      setTimeout(() => document.body.removeChild(notification), 3000);
    } catch (error) {
      console.error('Failed to play track:', error);
      alert('Failed to play track: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [`play-${track.id}`]: false }));
    }
  };

  const formatDuration = (seconds) => {
    if (!seconds) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Music Library</h1>
          <p className="text-gray-600">Manage your music collection</p>
        </div>
        
        <button
          onClick={() => {
            console.log('Upload button clicked');
            fileInputRef.current?.click();
          }}
          disabled={uploading}
          className="btn-primary flex items-center space-x-2"
        >
          {uploading ? (
            <div className="spinner"></div>
          ) : (
            <Upload className="w-5 h-5" />
          )}
          <span>{uploading ? 'Uploading...' : 'Upload Music'}</span>
        </button>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="audio/*"
          onChange={handleFileUpload}
          className="hidden"
        />
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <div className="flex items-center">
            <Music className="w-8 h-8 text-blue-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Total Tracks</p>
              <p className="text-2xl font-bold text-gray-900">{state.tracks.length}</p>
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="flex items-center">
            <Clock className="w-8 h-8 text-green-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Total Duration</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatDuration(state.tracks.reduce((sum, track) => sum + (track.duration || 0), 0))}
              </p>
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="flex items-center">
            <User className="w-8 h-8 text-purple-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Artists</p>
              <p className="text-2xl font-bold text-gray-900">
                {new Set(state.tracks.map(track => track.artist).filter(Boolean)).size}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="card">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search tracks, artists, albums..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input-field pl-10"
          />
        </div>
      </div>

      {/* Track List */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900">
            Tracks ({filteredTracks.length})
          </h2>
        </div>

        {state.loading ? (
          <div className="text-center py-12">
            <div className="spinner mx-auto mb-4"></div>
            <p className="text-gray-500">Loading tracks...</p>
          </div>
        ) : filteredTracks.length === 0 ? (
          <div className="text-center py-12">
            <Music className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {state.tracks.length === 0 ? 'No music uploaded yet' : 'No tracks found'}
            </h3>
            <p className="text-gray-500 mb-6">
              {state.tracks.length === 0 
                ? 'Upload your first audio files to get started'
                : 'Try adjusting your search terms'
              }
            </p>
            {state.tracks.length === 0 && (
              <button
                onClick={() => fileInputRef.current?.click()}
                className="btn-primary"
              >
                Upload Music
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Track
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Artist
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Album
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Size
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTracks.map((track) => (
                  <tr key={track.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gradient-to-br from-radio-400 to-radio-600 rounded-lg flex items-center justify-center mr-3">
                          <Music className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {track.title || track.original_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {track.genre && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                                {track.genre}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {track.artist || 'Unknown Artist'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {track.album || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDuration(track.duration)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatFileSize(track.size)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-1">
                        <button
                          onClick={() => handleAddToQueue(track)}
                          disabled={actionLoading[`queue-${track.id}`]}
                          className={`p-2 rounded-lg transition-all duration-200 group ${
                            actionLoading[`queue-${track.id}`]
                              ? 'text-gray-400 cursor-not-allowed'
                              : 'text-radio-600 hover:text-white hover:bg-radio-600'
                          }`}
                          title="Add to queue"
                        >
                          {actionLoading[`queue-${track.id}`] ? (
                            <div className="w-4 h-4 border-2 border-radio-600 border-t-transparent rounded-full animate-spin"></div>
                          ) : (
                            <Plus className="w-4 h-4" />
                          )}
                        </button>
                        <button
                          onClick={() => handlePlayNow(track)}
                          disabled={actionLoading[`play-${track.id}`]}
                          className={`p-2 rounded-lg transition-all duration-200 group ${
                            actionLoading[`play-${track.id}`]
                              ? 'text-gray-400 cursor-not-allowed'
                              : 'text-green-600 hover:text-white hover:bg-green-600'
                          }`}
                          title="Play now"
                        >
                          {actionLoading[`play-${track.id}`] ? (
                            <div className="w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin"></div>
                          ) : (
                            <Play className="w-4 h-4" />
                          )}
                        </button>
                        <button
                          onClick={() => {/* More options - TODO: Add dropdown menu */}}
                          className="p-2 text-gray-400 hover:text-white hover:bg-gray-600 rounded-lg transition-all duration-200 group"
                          title="More options"
                        >
                          <MoreVertical className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
