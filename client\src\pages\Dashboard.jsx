import React from 'react';
import { useRadio } from '../context/RadioContext';
import {
  Music,
  List,
  Megaphone,
  Clock,
  TrendingUp,
  Users,
  Radio,
  Activity,
  Wifi,
  WifiOff,
  ExternalLink
} from 'lucide-react';
import MusicPlayer from '../components/MusicPlayer';
import SampleDeck from '../components/SampleDeck';
import VolumeControl from '../components/VolumeControl';
import DJTurntable from '../components/DJTurntable';
import QueueDisplay from '../components/QueueDisplay';

export default function Dashboard() {
  const { state } = useRadio();

  const stats = [
    {
      name: 'Total Tracks',
      value: state.stats.totalTracks,
      icon: Music,
      color: 'bg-blue-500'
    },
    {
      name: 'Playlists',
      value: state.stats.totalPlaylists,
      icon: List,
      color: 'bg-green-500'
    },
    {
      name: 'Advertisements',
      value: state.stats.totalAds,
      icon: Megaphone,
      color: 'bg-yellow-500'
    },
    {
      name: 'Uptime',
      value: formatUptime(state.stats.uptime),
      icon: Clock,
      color: 'bg-purple-500'
    }
  ];

  function formatUptime(seconds) {
    if (!seconds) return '0m';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }

  function formatDuration(seconds) {
    if (!seconds) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Monitor your radio station's performance and activity</p>
        </div>

        {/* Master Volume Control */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center space-x-3">
            <span className="text-sm font-medium text-gray-700">Master Volume</span>
            <VolumeControl size="normal" showLabel={true} />
          </div>
        </div>
      </div>

      {/* DJ Turntable Interface */}
      <DJTurntable />

      {/* Sample Deck */}
      <SampleDeck />

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className={`p-3 rounded-lg ${stat.color}`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Current Status */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Now Playing */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Now Playing</h2>
            <Radio className="w-5 h-5 text-radio-500" />
          </div>
          
          {state.currentTrack ? (
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-16 h-16 bg-gradient-to-br from-radio-400 to-radio-600 rounded-lg flex items-center justify-center">
                  <Music className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">
                    {state.currentTrack.title || state.currentTrack.original_name || state.currentTrack.originalName}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {state.currentTrack.artist || 'Unknown Artist'}
                  </p>
                  <p className="text-xs text-gray-400">
                    {state.currentTrack.album && `${state.currentTrack.album} • `}
                    {formatDuration(state.currentTrack.duration)}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  {state.isPlaying ? (
                    <>
                      <Activity className="w-5 h-5 text-green-500" />
                      <span className="text-sm font-medium text-green-600">Live</span>
                    </>
                  ) : (
                    <>
                      <div className="w-5 h-5 rounded-full bg-gray-300"></div>
                      <span className="text-sm font-medium text-gray-500">Paused</span>
                    </>
                  )}
                </div>
              </div>
              
              {/* Progress bar placeholder */}
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-radio-500 h-2 rounded-full transition-all duration-1000"
                  style={{ width: state.isPlaying ? '45%' : '0%' }}
                ></div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Radio className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No track currently playing</p>
              <p className="text-sm text-gray-400">Upload music and start broadcasting</p>
            </div>
          )}
        </div>

        {/* Queue Display */}
        <QueueDisplay />

        {/* Live Streaming */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Live Streaming</h2>
            <Radio className="w-5 h-5 text-radio-500" />
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {state.streaming.isStreaming ? (
                  <>
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium text-green-600">Live</span>
                  </>
                ) : (
                  <>
                    <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                    <span className="text-sm font-medium text-gray-500">Offline</span>
                  </>
                )}
              </div>

              <button
                onClick={() => {
                  if (state.streaming.isStreaming) {
                    api.stopStreaming();
                  } else {
                    api.startStreaming();
                  }
                }}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  state.streaming.isStreaming
                    ? 'bg-red-100 text-red-700 hover:bg-red-200'
                    : 'bg-green-100 text-green-700 hover:bg-green-200'
                }`}
              >
                {state.streaming.isStreaming ? 'Stop Stream' : 'Start Stream'}
              </button>
            </div>

            {state.streaming.isStreaming && (
              <div className="space-y-2">
                <div className="text-sm text-gray-600">
                  <strong>Stream URL:</strong>
                  <br />
                  <a
                    href={state.streaming.streamUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-radio-600 hover:text-radio-800 break-all"
                  >
                    {state.streaming.streamUrl}
                  </a>
                </div>

                <div className="text-sm text-gray-600">
                  <strong>Playlist URL:</strong>
                  <br />
                  <a
                    href={state.streaming.playlistUrl}
                    download="stream.m3u"
                    className="text-radio-600 hover:text-radio-800 break-all"
                  >
                    {state.streaming.playlistUrl}
                  </a>
                </div>

                <div className="flex justify-between text-sm text-gray-500">
                  <span>Listeners: {state.streaming.connectedClients}</span>
                  <span>Bitrate: {state.streaming.bitrate}k</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
          <TrendingUp className="w-5 h-5 text-radio-500" />
        </div>
        
        <div className="space-y-3">
          <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-700">Radio station started</span>
            <span className="text-xs text-gray-500 ml-auto">Just now</span>
          </div>
          
          {state.tracks.length > 0 && (
            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-gray-700">
                {state.tracks.length} tracks loaded in library
              </span>
              <span className="text-xs text-gray-500 ml-auto">Recently</span>
            </div>
          )}
          
          {state.ads.length > 0 && (
            <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span className="text-sm text-gray-700">
                {state.ads.length} advertisements ready
              </span>
              <span className="text-xs text-gray-500 ml-auto">Recently</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
