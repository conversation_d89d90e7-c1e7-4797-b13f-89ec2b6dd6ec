import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  Home,
  Music,
  List,
  Megaphone,
  Calendar,
  Settings,
  Radio,
  Disc
} from 'lucide-react';

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'DJ Player', href: '/dj', icon: Disc },
  { name: 'Music Library', href: '/music', icon: Music },
  { name: 'Playlists', href: '/playlists', icon: List },
  { name: 'Advertisements', href: '/ads', icon: Megaphone },
  { name: 'Schedule', href: '/schedule', icon: Calendar },
  { name: 'Settings', href: '/settings', icon: Settings },
];

export default function Sidebar() {
  return (
    <div className="flex flex-col w-64 bg-white shadow-lg">
      {/* Logo */}
      <div className="flex items-center justify-center h-16 px-4 bg-radio-600">
        <Radio className="w-8 h-8 text-white mr-2" />
        <h1 className="text-xl font-bold text-white">Smart System</h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => (
          <NavLink
            key={item.name}
            to={item.href}
            className={({ isActive }) =>
              `nav-link ${isActive ? 'nav-link-active' : 'nav-link-inactive'}`
            }
          >
            <item.icon className="w-5 h-5 mr-3" />
            {item.name}
          </NavLink>
        ))}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 text-center">
          Smart Radio Station v1.0

          <p>This System is under development</p>
        </div>
      </div>
    </div>
  );
}
