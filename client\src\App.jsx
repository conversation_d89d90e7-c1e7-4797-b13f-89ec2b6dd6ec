import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { io } from 'socket.io-client';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import WebAudioPlayer from './components/WebAudioPlayer';
import Dashboard from './pages/Dashboard';
import MusicLibrary from './pages/MusicLibrary';
import Playlists from './pages/Playlists';
import Ads from './pages/Ads';
import Schedule from './pages/Schedule';
import Settings from './pages/Settings';
import DJPlayer from './pages/DJPlayer';
import { RadioProvider } from './context/RadioContext';

function App() {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Initialize socket connection
    const serverUrl = import.meta.env.VITE_API_URL || 'http://localhost:5002';
    console.log('Connecting to server:', serverUrl);

    try {
      const newSocket = io(serverUrl, {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionAttempts: 5,
        maxReconnectionAttempts: 5
      });

      newSocket.on('connect', () => {
        console.log('✅ Connected to server');
        setIsConnected(true);
      });

      newSocket.on('disconnect', (reason) => {
        console.log('❌ Disconnected from server:', reason);
        setIsConnected(false);
      });

      newSocket.on('connect_error', (error) => {
        console.error('❌ Connection error:', error);
        setIsConnected(false);
      });

      setSocket(newSocket);

      return () => {
        console.log('🔌 Closing socket connection');
        newSocket.close();
      };
    } catch (error) {
      console.error('❌ Failed to initialize socket:', error);
      setIsConnected(false);
    }
  }, []);

  return (
    <RadioProvider socket={socket}>
      <Router>
        <div className="flex h-screen bg-gray-50">
          {/* Sidebar */}
          <Sidebar />
          
          {/* Main content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Header */}
            <Header isConnected={isConnected} />

            {/* Web Audio Player */}
            <WebAudioPlayer />

            {/* Page content */}
            <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/music" element={<MusicLibrary />} />
                <Route path="/playlists" element={<Playlists />} />
                <Route path="/ads" element={<Ads />} />
                <Route path="/schedule" element={<Schedule />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="/dj" element={<DJPlayer />} />
              </Routes>
            </main>
          </div>
        </div>
      </Router>
    </RadioProvider>
  );
}

export default App;
