import React, { createContext, useContext, useReducer, useEffect } from 'react';
import axios from 'axios';

const RadioContext = createContext();

const initialState = {
  // Player state
  currentTrack: null,
  isPlaying: false,
  volume: 0.8,
  queue: [],
  currentIndex: 0,
  
  // Library
  tracks: [],
  playlists: [],
  ads: [],
  
  // Schedule
  schedules: [],
  currentSchedule: null,
  
  // UI state
  loading: false,
  error: null,
  
  // Stats
  stats: {
    totalTracks: 0,
    totalPlaylists: 0,
    totalAds: 0,
    uptime: 0
  },

  // Streaming
  streaming: {
    isStreaming: false,
    streamUrl: '',
    playlistUrl: '',
    connectedClients: 0,
    bitrate: 128
  },

  // Player state
  player: {
    currentTime: 0,
    duration: 0,
    buffered: 0
  }
};

function radioReducer(state, action) {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
      
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
      
    case 'SET_CURRENT_TRACK':
      return { ...state, currentTrack: action.payload };
      
    case 'SET_PLAYING':
      return { ...state, isPlaying: action.payload };
      
    case 'SET_VOLUME':
      return { ...state, volume: action.payload };
      
    case 'SET_QUEUE':
      return { ...state, queue: action.payload };
      
    case 'SET_TRACKS':
      return { 
        ...state, 
        tracks: action.payload,
        stats: { ...state.stats, totalTracks: action.payload.length }
      };
      
    case 'ADD_TRACK':
      const newTracks = [...state.tracks, action.payload];
      return { 
        ...state, 
        tracks: newTracks,
        stats: { ...state.stats, totalTracks: newTracks.length }
      };
      
    case 'SET_PLAYLISTS':
      return { 
        ...state, 
        playlists: action.payload,
        stats: { ...state.stats, totalPlaylists: action.payload.length }
      };
      
    case 'ADD_PLAYLIST':
      const newPlaylists = [...state.playlists, action.payload];
      return { 
        ...state, 
        playlists: newPlaylists,
        stats: { ...state.stats, totalPlaylists: newPlaylists.length }
      };
      
    case 'SET_ADS':
      return { 
        ...state, 
        ads: action.payload,
        stats: { ...state.stats, totalAds: action.payload.length }
      };
      
    case 'ADD_AD':
      const newAds = [...state.ads, action.payload];
      return { 
        ...state, 
        ads: newAds,
        stats: { ...state.stats, totalAds: newAds.length }
      };
      
    case 'SET_SCHEDULES':
      return { ...state, schedules: action.payload };
      
    case 'SET_CURRENT_SCHEDULE':
      return { ...state, currentSchedule: action.payload };
      
    case 'UPDATE_STATS':
      return { ...state, stats: { ...state.stats, ...action.payload } };

    case 'SET_STREAMING_INFO':
      return { ...state, streaming: { ...state.streaming, ...action.payload } };

    case 'UPDATE_PLAYER_TIME':
      return {
        ...state,
        player: {
          ...state.player,
          currentTime: action.payload.currentTime,
          duration: action.payload.duration || state.player.duration
        }
      };

    case 'SEEK_TO':
      return {
        ...state,
        player: {
          ...state.player,
          currentTime: action.payload
        }
      };

    case 'ADD_TO_QUEUE':
      return {
        ...state,
        queue: [...state.queue, action.payload]
      };

    default:
      return state;
  }
}

export function RadioProvider({ children, socket }) {
  const [state, dispatch] = useReducer(radioReducer, initialState);

  // API base URL
  const API_BASE = import.meta.env.VITE_API_URL || 'http://localhost:5002';

  // Socket event handlers
  useEffect(() => {
    if (!socket) return;

    socket.on('status', (data) => {
      dispatch({ type: 'SET_CURRENT_TRACK', payload: data.currentTrack });
      dispatch({ type: 'SET_PLAYING', payload: data.isPlaying });
      dispatch({ type: 'SET_VOLUME', payload: data.volume });
      dispatch({ type: 'SET_QUEUE', payload: data.queue });
    });

    socket.on('trackChanged', (data) => {
      dispatch({ type: 'SET_CURRENT_TRACK', payload: data.track });
      dispatch({ type: 'SET_QUEUE', payload: data.queue });
    });

    socket.on('playStateChanged', (data) => {
      dispatch({ type: 'SET_PLAYING', payload: data.isPlaying });
    });

    socket.on('adStarted', (data) => {
      // Handle ad started
      console.log('Ad started:', data.ad);
    });

    socket.on('adEnded', () => {
      // Handle ad ended
      console.log('Ad ended');
    });

    return () => {
      socket.off('status');
      socket.off('trackChanged');
      socket.off('playStateChanged');
      socket.off('adStarted');
      socket.off('adEnded');
    };
  }, [socket]);

  // API functions
  const api = {
    // Player controls
    async play() {
      try {
        await axios.post(`${API_BASE}/api/control/play`);
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
      }
    },

    async pause() {
      try {
        await axios.post(`${API_BASE}/api/control/pause`);
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
      }
    },

    async next() {
      try {
        await axios.post(`${API_BASE}/api/control/next`);
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
      }
    },

    async setVolume(volume) {
      try {
        await axios.post(`${API_BASE}/api/control/volume`, { volume });
        dispatch({ type: 'SET_VOLUME', payload: volume });
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
      }
    },

    // Library management
    async fetchTracks() {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        const response = await axios.get(`${API_BASE}/api/playlist`);
        dispatch({ type: 'SET_TRACKS', payload: response.data });
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    async uploadTrack(file, metadata = {}) {
      try {
        console.log('API uploadTrack called with:', file.name, file.type, file.size);
        console.log('API_BASE:', API_BASE);

        dispatch({ type: 'SET_LOADING', payload: true });
        const formData = new FormData();
        formData.append('audio', file);
        Object.keys(metadata).forEach(key => {
          formData.append(key, metadata[key]);
        });

        console.log('Making POST request to:', `${API_BASE}/api/upload/music`);
        const response = await axios.post(`${API_BASE}/api/upload/music`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });

        console.log('Upload response:', response.data);
        dispatch({ type: 'ADD_TRACK', payload: response.data.track });
        return response.data.track;
      } catch (error) {
        console.error('API upload error:', error);
        console.error('Error response:', error.response?.data);
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    async fetchPlaylists() {
      try {
        // This would be implemented when playlist API is ready
        dispatch({ type: 'SET_PLAYLISTS', payload: [] });
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
      }
    },

    async fetchAds() {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        const response = await axios.get(`${API_BASE}/api/ads`);
        dispatch({ type: 'SET_ADS', payload: response.data });
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    async uploadAd(file, metadata = {}) {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        const formData = new FormData();
        formData.append('audio', file);
        Object.keys(metadata).forEach(key => {
          formData.append(key, metadata[key]);
        });

        const response = await axios.post(`${API_BASE}/api/upload/ad`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });

        dispatch({ type: 'ADD_AD', payload: response.data.ad });
        return response.data.ad;
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    async fetchStatus() {
      try {
        const response = await axios.get(`${API_BASE}/api/status`);
        dispatch({ type: 'SET_CURRENT_TRACK', payload: response.data.currentTrack });
        dispatch({ type: 'SET_PLAYING', payload: response.data.isPlaying });
        dispatch({ type: 'SET_VOLUME', payload: response.data.volume });
        dispatch({ type: 'SET_QUEUE', payload: response.data.queue });
        dispatch({ type: 'UPDATE_STATS', payload: { uptime: response.data.uptime } });
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
      }
    },

    async fetchStreamingInfo() {
      try {
        const response = await axios.get(`${API_BASE}/api/stream/info`);
        dispatch({ type: 'SET_STREAMING_INFO', payload: response.data });
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
      }
    },

    async startStreaming() {
      try {
        await axios.post(`${API_BASE}/api/stream/start`);
        await this.fetchStreamingInfo();
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    },

    async stopStreaming() {
      try {
        await axios.post(`${API_BASE}/api/stream/stop`);
        await this.fetchStreamingInfo();
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    },

    // Seek to specific time
    seekTo(time) {
      dispatch({ type: 'SEEK_TO', payload: time });
    },

    // Add track to queue
    async addToQueue(track) {
      try {
        const response = await axios.post(`${API_BASE}/api/queue/add`, { trackId: track.id });
        dispatch({ type: 'ADD_TO_QUEUE', payload: track });
        return response.data;
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    },

    // Play track immediately
    async playTrack(track) {
      try {
        const response = await axios.post(`${API_BASE}/api/control/play-track`, { trackId: track.id });
        dispatch({ type: 'SET_CURRENT_TRACK', payload: track });
        dispatch({ type: 'SET_PLAYING', payload: true });
        return response.data;
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    },

    // Create new playlist
    async createPlaylist(playlistData) {
      try {
        const response = await axios.post(`${API_BASE}/api/playlists`, playlistData);
        await this.fetchPlaylists(); // Refresh playlists
        return response.data;
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    }
  };

  // Load initial data
  useEffect(() => {
    api.fetchStatus();
    api.fetchTracks();
    api.fetchPlaylists();
    api.fetchAds();
    api.fetchStreamingInfo();
  }, []);

  const value = {
    state,
    dispatch,
    api
  };

  return (
    <RadioContext.Provider value={value}>
      {children}
    </RadioContext.Provider>
  );
}

export function useRadio() {
  const context = useContext(RadioContext);
  if (!context) {
    throw new Error('useRadio must be used within a RadioProvider');
  }
  return context;
}
