const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class DatabaseManager {
  constructor() {
    this.dbPath = path.join(__dirname, '../data/radio.db');
    this.db = null;
  }

  async initialize() {
    try {
      // Ensure data directory exists
      const dataDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      // Open database connection
      this.db = new sqlite3.Database(this.dbPath);
      
      // Create tables
      await this.createTables();
      
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  async createTables() {
    const tables = [
      // Tracks table
      `CREATE TABLE IF NOT EXISTS tracks (
        id TEXT PRIMARY KEY,
        filename TEXT NOT NULL,
        original_name TEXT NOT NULL,
        path TEXT NOT NULL,
        size INTEGER,
        duration REAL,
        artist TEXT,
        title TEXT,
        album TEXT,
        genre TEXT,
        year INTEGER,
        uploaded_at TEXT,
        play_count INTEGER DEFAULT 0,
        last_played TEXT,
        metadata TEXT
      )`,
      
      // Ads table
      `CREATE TABLE IF NOT EXISTS ads (
        id TEXT PRIMARY KEY,
        filename TEXT NOT NULL,
        original_name TEXT NOT NULL,
        path TEXT NOT NULL,
        size INTEGER,
        duration REAL,
        category TEXT,
        tags TEXT,
        priority TEXT DEFAULT 'normal',
        uploaded_at TEXT,
        play_count INTEGER DEFAULT 0,
        last_played TEXT,
        metadata TEXT
      )`,
      
      // Playlists table
      `CREATE TABLE IF NOT EXISTS playlists (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        track_ids TEXT, -- JSON array of track IDs
        tracks_data TEXT, -- JSON array of track data
        created_at TEXT,
        total_tracks INTEGER DEFAULT 0,
        total_duration INTEGER DEFAULT 0,
        description TEXT,
        updated_at TEXT,
        is_active BOOLEAN DEFAULT 0,
        metadata TEXT
      )`,
      
      // Playlist tracks junction table
      `CREATE TABLE IF NOT EXISTS playlist_tracks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        playlist_id TEXT,
        track_id TEXT,
        position INTEGER,
        FOREIGN KEY (playlist_id) REFERENCES playlists (id),
        FOREIGN KEY (track_id) REFERENCES tracks (id)
      )`,
      
      // Schedules table
      `CREATE TABLE IF NOT EXISTS schedules (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL, -- 'playlist', 'ad', 'live'
        target_id TEXT, -- playlist_id or ad_id
        start_time TEXT,
        end_time TEXT,
        days_of_week TEXT, -- JSON array
        is_active BOOLEAN DEFAULT 1,
        priority INTEGER DEFAULT 0,
        created_at TEXT,
        metadata TEXT
      )`,
      
      // Play history table
      `CREATE TABLE IF NOT EXISTS play_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        track_id TEXT,
        ad_id TEXT,
        played_at TEXT,
        duration_played REAL,
        skipped BOOLEAN DEFAULT 0,
        FOREIGN KEY (track_id) REFERENCES tracks (id),
        FOREIGN KEY (ad_id) REFERENCES ads (id)
      )`,
      
      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT,
        updated_at TEXT
      )`,
      
      // Analytics table
      `CREATE TABLE IF NOT EXISTS analytics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        event_type TEXT,
        event_data TEXT,
        timestamp TEXT
      )`
    ];

    for (const tableSQL of tables) {
      await this.run(tableSQL);
    }
  }

  // Promisify database operations
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // Track operations
  async addTrack(trackData) {
    const sql = `INSERT INTO tracks (
      id, filename, original_name, path, size, duration,
      artist, title, album, genre, year, uploaded_at, metadata
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
    const params = [
      trackData.id,
      trackData.filename,
      trackData.originalName,
      trackData.path,
      trackData.size,
      trackData.duration || null,
      trackData.artist || null,
      trackData.title || null,
      trackData.album || null,
      trackData.genre || null,
      trackData.year || null,
      trackData.uploadedAt,
      JSON.stringify(trackData.metadata || {})
    ];
    
    return await this.run(sql, params);
  }

  async getAllTracks() {
    const sql = 'SELECT * FROM tracks ORDER BY uploaded_at DESC';
    return await this.all(sql);
  }

  async getTrackById(id) {
    const sql = 'SELECT * FROM tracks WHERE id = ?';
    return await this.get(sql, [id]);
  }

  async updateTrackPlayCount(id) {
    const sql = `UPDATE tracks SET 
      play_count = play_count + 1, 
      last_played = ? 
      WHERE id = ?`;
    return await this.run(sql, [new Date().toISOString(), id]);
  }

  // Ad operations
  async addAd(adData) {
    const sql = `INSERT INTO ads (
      id, filename, original_name, path, size, duration,
      category, tags, priority, uploaded_at, metadata
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
    const params = [
      adData.id,
      adData.filename,
      adData.originalName,
      adData.path,
      adData.size,
      adData.duration || null,
      adData.category || null,
      JSON.stringify(adData.tags || []),
      adData.priority || 'normal',
      adData.uploadedAt,
      JSON.stringify(adData.metadata || {})
    ];
    
    return await this.run(sql, params);
  }

  async getAllAds() {
    const sql = 'SELECT * FROM ads ORDER BY uploaded_at DESC';
    return await this.all(sql);
  }

  // Playlist operations
  async createPlaylist(playlistData) {
    const sql = `INSERT INTO playlists (
      id, name, description, created_at, updated_at, metadata
    ) VALUES (?, ?, ?, ?, ?, ?)`;
    
    const now = new Date().toISOString();
    const params = [
      playlistData.id,
      playlistData.name,
      playlistData.description || null,
      now,
      now,
      JSON.stringify(playlistData.metadata || {})
    ];
    
    return await this.run(sql, params);
  }

  async getAllPlaylists() {
    const sql = 'SELECT * FROM playlists ORDER BY created_at DESC';
    return await this.all(sql);
  }

  async addTrackToPlaylist(playlistId, trackId, position) {
    const sql = `INSERT INTO playlist_tracks (playlist_id, track_id, position) 
                 VALUES (?, ?, ?)`;
    return await this.run(sql, [playlistId, trackId, position]);
  }

  async getPlaylistTracks(playlistId) {
    const sql = `SELECT t.*, pt.position 
                 FROM tracks t 
                 JOIN playlist_tracks pt ON t.id = pt.track_id 
                 WHERE pt.playlist_id = ? 
                 ORDER BY pt.position`;
    return await this.all(sql, [playlistId]);
  }

  // Schedule operations
  async addSchedule(scheduleData) {
    const sql = `INSERT INTO schedules (
      id, name, type, target_id, start_time, end_time,
      days_of_week, is_active, priority, created_at, metadata
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
    const params = [
      scheduleData.id,
      scheduleData.name,
      scheduleData.type,
      scheduleData.targetId,
      scheduleData.startTime,
      scheduleData.endTime,
      JSON.stringify(scheduleData.daysOfWeek || []),
      scheduleData.isActive !== false ? 1 : 0,
      scheduleData.priority || 0,
      new Date().toISOString(),
      JSON.stringify(scheduleData.metadata || {})
    ];
    
    return await this.run(sql, params);
  }

  async getActiveSchedules() {
    const sql = 'SELECT * FROM schedules WHERE is_active = 1 ORDER BY priority DESC';
    return await this.all(sql);
  }

  // Play history
  async addPlayHistory(data) {
    const sql = `INSERT INTO play_history (
      track_id, ad_id, played_at, duration_played, skipped
    ) VALUES (?, ?, ?, ?, ?)`;
    
    const params = [
      data.trackId || null,
      data.adId || null,
      data.playedAt || new Date().toISOString(),
      data.durationPlayed || null,
      data.skipped ? 1 : 0
    ];
    
    return await this.run(sql, params);
  }

  // Settings
  async setSetting(key, value) {
    const sql = `INSERT OR REPLACE INTO settings (key, value, updated_at) 
                 VALUES (?, ?, ?)`;
    return await this.run(sql, [key, value, new Date().toISOString()]);
  }

  async getSetting(key) {
    const sql = 'SELECT value FROM settings WHERE key = ?';
    const result = await this.get(sql, [key]);
    return result ? result.value : null;
  }

  // Analytics
  async logEvent(eventType, eventData) {
    const sql = `INSERT INTO analytics (event_type, event_data, timestamp) 
                 VALUES (?, ?, ?)`;
    return await this.run(sql, [
      eventType,
      JSON.stringify(eventData),
      new Date().toISOString()
    ]);
  }

  // Cleanup and maintenance
  async cleanup() {
    // Remove old play history (keep last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const sql = 'DELETE FROM play_history WHERE played_at < ?';
    return await this.run(sql, [thirtyDaysAgo.toISOString()]);
  }

  close() {
    if (this.db) {
      this.db.close();
    }
  }
}

module.exports = DatabaseManager;
